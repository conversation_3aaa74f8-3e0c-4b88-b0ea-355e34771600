'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { CheckIcon } from 'lucide-react';
import { createCheckoutSession, CreateCheckoutSessionResponse } from '@/lib/api';
import { useAuth } from '@/components/AuthProvider';
import { SUBSCRIPTION_TIERS } from '@/lib/config';
import { toast } from 'sonner';

interface PricingSectionCompactProps {
  returnUrl?: string;
  hideFree?: boolean;
}

export function PricingSectionCompact({ returnUrl = '/', hideFree = false }: PricingSectionCompactProps) {
  const { session } = useAuth();
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({});

  const handleSubscribe = async (priceId: string) => {
    if (!session) {
      window.location.href = '/auth';
      return;
    }

    if (isLoading[priceId]) return;

    try {
      setIsLoading(prev => ({ ...prev, [priceId]: true }));
      
      const response: CreateCheckoutSessionResponse = await createCheckoutSession({
        price_id: priceId,
        success_url: returnUrl,
        cancel_url: returnUrl,
      });

      if (response.url) {
        window.location.href = response.url;
      } else {
        toast.error('Erreur lors de la création de la session de paiement');
      }
    } catch (error: any) {
      console.error('Error creating checkout session:', error);
      const errorMessage = error?.response?.data?.detail || error?.message || 'Erreur lors du traitement du paiement';
      toast.error(errorMessage);
    } finally {
      setIsLoading(prev => ({ ...prev, [priceId]: false }));
    }
  };

  const plans = [
    {
      name: 'Découverte',
      price: 'Gratuit',
      priceId: SUBSCRIPTION_TIERS.FREE.priceId,
      description: 'Idéal pour découvrir Orchestra',
      missions: '6 missions/mois',
      time: '30 min d\'utilisation',
      features: [
        'Accès à Alex (IA généraliste)',
        'Fonctionnalités de base',
        'Assistance par email'
      ],
      buttonText: 'Plan Actuel',
      buttonVariant: 'outline' as const,
      isCurrent: true
    },
    {
      name: 'Étudiant',
      price: '29€',
      priceId: SUBSCRIPTION_TIERS.TIER_2_20.priceId,
      description: 'Idéal pour les étudiants',
      missions: '24 missions/mois',
      time: '2h d\'utilisation',
      features: [
        'Tout ce qui est inclus dans Découverte',
        'Génération de contenu avancée',
        'Assistance premium'
      ],
      buttonText: 'S\'abonner',
      buttonVariant: 'default' as const
    },
    {
      name: 'Essentiel',
      price: '59€',
      priceId: SUBSCRIPTION_TIERS.TIER_6_50.priceId,
      description: 'Pour les indépendants et petites entreprises',
      missions: '72 missions/mois',
      time: '6h d\'utilisation',
      features: [
        'Tout ce qui est inclus dans Étudiant',
        'Analyse de données avancée',
        'Accès aux modèles spécialisés'
      ],
      buttonText: 'S\'abonner',
      buttonVariant: 'default' as const,
      isPopular: true
    },
    {
      name: 'Pro',
      price: '199€',
      priceId: SUBSCRIPTION_TIERS.TIER_12_100.priceId,
      description: 'Pour les PME avec de nombreux besoins récurrents',
      missions: '144 missions/mois',
      time: '12h d\'utilisation',
      features: [
        'Tout ce qui est inclus dans Essentiel',
        'Support prioritaire',
        'Intégrations avancées'
      ],
      buttonText: 'S\'abonner',
      buttonVariant: 'default' as const
    },
    {
      name: 'Entreprise',
      price: '599€',
      priceId: SUBSCRIPTION_TIERS.TIER_50_400.priceId,
      description: 'Pour les grandes organisations',
      missions: '600 missions/mois',
      time: '50h d\'utilisation',
      features: [
        'Tout ce qui est inclus dans Pro',
        'Support dédié',
        'Fonctionnalités entreprise'
      ],
      buttonText: 'S\'abonner',
      buttonVariant: 'default' as const
    }
  ];

  return (
    <div className="w-full">
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold mb-2">Choisissez votre forfait</h3>
        <p className="text-sm text-muted-foreground">
          Passez à un forfait premium pour plus de missions avec Alex
        </p>
      </div>

      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
        {plans.filter(plan => !hideFree || plan.price !== 'Gratuit').map((plan) => (
          <div
            key={plan.name}
            className={`rounded-lg border p-4 relative ${
              plan.isPopular 
                ? 'border-primary/50 bg-primary/5' 
                : 'border-border bg-card'
            }`}
          >
            {plan.isPopular && (
              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground text-xs font-medium px-2 py-1 rounded-full">
                Populaire
              </div>
            )}
            
            <div className="mb-4">
              <h4 className="font-semibold text-sm">{plan.name}</h4>
              <div className="flex items-baseline gap-1 mt-1">
                <span className="text-xl font-bold">{plan.price}</span>
                {plan.price !== 'Gratuit' && (
                  <span className="text-muted-foreground text-xs">/mois</span>
                )}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {plan.description}
              </p>
            </div>

            <div className="mb-4">
              <div className="bg-muted/40 rounded p-2 mb-3">
                <p className="text-xs font-medium">{plan.missions}</p>
                <p className="text-xs text-muted-foreground">{plan.time}</p>
              </div>

              <ul className="space-y-1.5">
                {plan.features.slice(0, 2).map((feature, index) => (
                  <li key={index} className="flex items-start text-xs">
                    <CheckIcon className="h-3 w-3 text-green-500 mr-1.5 mt-0.5 shrink-0" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>

            <Button
              onClick={() => plan.priceId && handleSubscribe(plan.priceId)}
              disabled={plan.isCurrent || isLoading[plan.priceId || '']}
              variant={plan.buttonVariant}
              size="sm"
              className="w-full text-xs"
            >
              {isLoading[plan.priceId || ''] ? 'Chargement...' : plan.buttonText}
            </Button>
          </div>
        ))}
      </div>

      <div className="mt-4 p-3 bg-muted/30 rounded-lg">
        <p className="text-xs text-muted-foreground text-center">
          💡 Une mission = environ 10 minutes d'utilisation d'Alex
        </p>
      </div>
    </div>
  );
}
