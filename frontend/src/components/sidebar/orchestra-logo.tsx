'use client';

import Image from 'next/image';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

interface OrchestraLogoProps {
  size?: number;
}

export function OrchestraLogo({ size = 10 }: OrchestraLogoProps) {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // After mount, we can access the theme
  useEffect(() => {
    setMounted(true);
  }, []);

  // Temporary fallback until logo files are added
  const logoSrc = '/favicon.png';

  return (
    <Image
      src={logoSrc}
      alt="Orchestra Connect"
      width={size * 3} // Adjusted for favicon
      height={size * 3}
      className="flex-shrink-0 object-contain rounded-sm"
    />
  );
}
