# Render.com Configuration pour CONNECT
# SÉCURISÉ - Toutes les clés sensibles sont configurées via l'interface Render

services:
  # Frontend Next.js
  - type: web
    name: connect-frontend
    runtime: docker
    plan: standard
    repo: https://github.com/LG15601/connect.git
    dockerfilePath: ./frontend/Dockerfile
    dockerContext: ./frontend
    envVars:
      # Public environment variables (safe to expose)
      - key: NEXT_PUBLIC_BACKEND_URL
        value: https://connect-backend-p2ex.onrender.com/api
      - key: NEXT_PUBLIC_URL
        value: https://orchestraconnect.fr
      - key: NEXT_PUBLIC_ENV_MODE
        value: production
      - key: NODE_ENV
        value: production
      - key: NEXT_PUBLIC_VERCEL_ENV
        value: production
      # Note: SUPABASE keys configured via Render Environment Variables

  # Backend API
  - type: web
    name: connect-backend
    runtime: docker
    plan: standard
    repo: https://github.com/LG15601/connect.git
    dockerfilePath: ./backend/Dockerfile
    dockerContext: ./backend
    envVars:
      # Basic Configuration
      - key: ENV_MODE
        value: production

      # Redis Configuration (Simple comme Docker local)
      - key: REDIS_HOST
        value: connect-redis
      - key: REDIS_PORT
        value: "6379"
      - key: REDIS_USERNAME
        value: ""
      - key: REDIS_PASSWORD
        value: ""
      - key: REDIS_SSL
        value: "false"
      - key: REDIS_URL
        value: "redis://connect-redis:6379"

      # RabbitMQ Configuration
      - key: RABBITMQ_HOST
        value: connect-rabbitmq
      - key: RABBITMQ_PORT
        value: "5672"
      - key: RABBITMQ_URL
        value: amqp://guest:guest@connect-rabbitmq:5672

      # Model Configuration
      - key: MODEL_TO_USE
        value: claude-3-5-sonnet-20241022

      # Service URLs
      - key: FIRECRAWL_URL
        value: https://api.firecrawl.dev
      - key: DAYTONA_SERVER_URL
        value: https://app.daytona.io/api
      - key: DAYTONA_TARGET
        value: us
      - key: LANGFUSE_HOST
        value: https://cloud.langfuse.com

      # Email Configuration
      - key: MAILTRAP_SENDER_EMAIL
        value: <EMAIL>
      - key: MAILTRAP_SENDER_NAME
        value: Orchestra Connect

      # Note: All sensitive API keys are configured via Render Environment Variables
      # - SUPABASE_URL, SUPABASE_ANON_KEY, SUPABASE_SERVICE_ROLE_KEY
      # - ANTHROPIC_API_KEY, OPENAI_API_KEY, OPENROUTER_API_KEY
      # - RAPID_API_KEY, TAVILY_API_KEY, FIRECRAWL_API_KEY, DAYTONA_API_KEY
      # - STRIPE_SECRET_KEY, STRIPE_WEBHOOK_SECRET
      # - MAILTRAP_API_TOKEN, LANGFUSE_PUBLIC_KEY, LANGFUSE_SECRET_KEY
      # - AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, GROQ_API_KEY, SMITHERY_API_KEY

  # Worker
  - type: worker
    name: connect-worker
    runtime: docker
    plan: standard
    repo: https://github.com/LG15601/connect.git
    dockerfilePath: ./backend/Dockerfile
    dockerContext: ./backend
    dockerCommand: python -m dramatiq run_agent_background
    envVars:
      # Environment Configuration
      - key: ENV_MODE
        value: production
      - key: PYTHONDONTWRITEBYTECODE
        value: "1"
      - key: PYTHONUNBUFFERED
        value: "1"

      # Redis Configuration (Simple comme Docker local)
      - key: REDIS_HOST
        value: connect-redis
      - key: REDIS_PORT
        value: "6379"
      - key: REDIS_USERNAME
        value: ""
      - key: REDIS_PASSWORD
        value: ""
      - key: REDIS_SSL
        value: "false"
      - key: REDIS_URL
        value: "redis://connect-redis:6379"

      # RabbitMQ Configuration
      - key: RABBITMQ_HOST
        value: connect-rabbitmq
      - key: RABBITMQ_PORT
        value: "5672"
      - key: RABBITMQ_URL
        value: amqp://guest:guest@connect-rabbitmq:5672

      # Model Configuration
      - key: MODEL_TO_USE
        value: claude-3-5-sonnet-20241022

      # Service URLs
      - key: FIRECRAWL_URL
        value: https://api.firecrawl.dev
      - key: DAYTONA_SERVER_URL
        value: https://app.daytona.io/api
      - key: DAYTONA_TARGET
        value: us
      - key: LANGFUSE_HOST
        value: https://cloud.langfuse.com

      # Note: All sensitive API keys are configured via Render Environment Variables
      # Same keys as backend service - configured via Render UI

  # Redis Service (Simple comme Docker local)
  - type: pserv
    name: connect-redis
    runtime: docker
    repo: https://github.com/LG15601/connect.git
    dockerfilePath: ./redis.Dockerfile
    plan: starter
    openPorts:
      - port: 6379
        protocol: tcp

  # RabbitMQ
  - type: pserv
    name: connect-rabbitmq
    runtime: docker
    repo: https://github.com/LG15601/connect.git
    dockerfilePath: ./rabbitmq.Dockerfile
    envVars:
      - key: RABBITMQ_URL
        value: amqp://guest:guest@localhost:5672
    openPorts:
      - port: 4369
        protocol: tcp
      - port: 5672
        protocol: tcp
      - port: 15692
        protocol: tcp
      - port: 25672
        protocol: tcp